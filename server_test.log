2025/08/07 09:24:10 logger.go:83: [INFO] 正在启动AI文本游戏服务器 version=1.0.0
数据库配置: DBName=dev.db, Host=, Port=0
使用SQLite数据库: dev.db
2025/08/07 09:24:10 logger.go:83: [INFO] 数据库连接成功
2025/08/07 09:24:10 logger.go:83: [INFO] 开始执行数据库迁移
2025/08/07 09:24:10 logger.go:83: [INFO] 检查数据库兼容性
2025/08/07 09:24:10 logger.go:83: [INFO] 数据库信息 type=sqlite supports_jsonb=false supports_uuid=false
2025/08/07 09:24:10 logger.go:83: [INFO] 开发环境：使用GORM自动迁移

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.021ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type='table' AND name="users"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.067ms] [34;1m[rows:7][0m SELECT sql FROM sqlite_master WHERE type IN ("table","index") AND tbl_name = "users" AND sql IS NOT NULL order by type = "table" desc

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.018ms] [34;1m[rows:-][0m SELECT * FROM `users` LIMIT 1

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[10.399ms] [34;1m[rows:0][0m ALTER TABLE `users` ADD `profile` text DEFAULT "{}"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.879ms] [34;1m[rows:0][0m ALTER TABLE `users` ADD `last_active_at` datetime

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.043ms] [34;1m[rows:1][0m PRAGMA foreign_keys

2025/08/07 09:24:10 [32m
[0m[33m[0.025ms] [34;1m[rows:-][0m SELECT sql FROM sqlite_master WHERE type = "table" AND tbl_name = "users" AND name = "users"

2025/08/07 09:24:10 [32m
[0m[33m[0.237ms] [34;1m[rows:0][0m CREATE TABLE `users__temp`  (`id` text,`external_id` text NOT NULL,`external_provider` text NOT NULL,`email` text NOT NULL,`display_name` text,`avatar_url` text,`game_roles` text DEFAULT "[""user""]",`status` text DEFAULT "active",`preferences` text DEFAULT "{}",`id_p_claims` text DEFAULT "{}",`created_at` datetime,`updated_at` datetime,`last_login_at` datetime,`deleted_at` datetime,`profile` text DEFAULT "{}",`last_active_at` datetime,PRIMARY KEY (`id`))

2025/08/07 09:24:10 [32m
[0m[33m[0.030ms] [34;1m[rows:0][0m INSERT INTO `users__temp`(`id`,`external_id`,`external_provider`,`email`,`display_name`,`avatar_url`,`game_roles`,`status`,`preferences`,`id_p_claims`,`created_at`,`updated_at`,`last_login_at`,`deleted_at`,`profile`,`last_active_at`) SELECT `id`,`external_id`,`external_provider`,`email`,`display_name`,`avatar_url`,`game_roles`,`status`,`preferences`,`id_p_claims`,`created_at`,`updated_at`,`last_login_at`,`deleted_at`,`profile`,`last_active_at` FROM `users`

2025/08/07 09:24:10 [32m
[0m[33m[0.519ms] [34;1m[rows:0][0m DROP TABLE `users`

2025/08/07 09:24:10 [32m
[0m[33m[0.406ms] [34;1m[rows:0][0m ALTER TABLE `users__temp` RENAME TO `users`

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.023ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "index" AND tbl_name = "users" AND name = "idx_users_external_id"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.778ms] [34;1m[rows:0][0m CREATE INDEX `idx_users_external_id` ON `users`(`external_id`)

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.035ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "index" AND tbl_name = "users" AND name = "idx_users_external_provider"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.725ms] [34;1m[rows:0][0m CREATE INDEX `idx_users_external_provider` ON `users`(`external_provider`)

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.028ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "index" AND tbl_name = "users" AND name = "idx_users_email"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[12.124ms] [34;1m[rows:0][0m CREATE INDEX `idx_users_email` ON `users`(`email`)

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.054ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "index" AND tbl_name = "users" AND name = "idx_users_last_active_at"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[8.118ms] [34;1m[rows:0][0m CREATE INDEX `idx_users_last_active_at` ON `users`(`last_active_at`)

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.042ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "index" AND tbl_name = "users" AND name = "idx_users_status"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.223ms] [34;1m[rows:0][0m CREATE INDEX `idx_users_status` ON `users`(`status`)

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.041ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "index" AND tbl_name = "users" AND name = "idx_users_created_at"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.743ms] [34;1m[rows:0][0m CREATE INDEX `idx_users_created_at` ON `users`(`created_at`)

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.046ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "index" AND tbl_name = "users" AND name = "idx_users_deleted_at"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.879ms] [34;1m[rows:0][0m CREATE INDEX `idx_users_deleted_at` ON `users`(`deleted_at`)

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.047ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type='table' AND name="user_stats"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.085ms] [34;1m[rows:3][0m SELECT sql FROM sqlite_master WHERE type IN ("table","index") AND tbl_name = "user_stats" AND sql IS NOT NULL order by type = "table" desc

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.026ms] [34;1m[rows:-][0m SELECT * FROM `user_stats` LIMIT 1

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.029ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "table" AND tbl_name = "user_stats" AND (sql LIKE "%CONSTRAINT ""fk_users_stats"" %" OR sql LIKE "%CONSTRAINT fk_users_stats %" OR sql LIKE "%CONSTRAINT `fk_users_stats`%" OR sql LIKE "%CONSTRAINT [fk_users_stats]%" OR sql LIKE "%CONSTRAINT 	fk_users_stats	%")

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.017ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "index" AND tbl_name = "user_stats" AND name = "idx_user_stats_level"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.016ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "index" AND tbl_name = "user_stats" AND name = "idx_user_stats_experience"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.031ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type='table' AND name="worlds"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.063ms] [34;1m[rows:6][0m SELECT sql FROM sqlite_master WHERE type IN ("table","index") AND tbl_name = "worlds" AND sql IS NOT NULL order by type = "table" desc

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.022ms] [34;1m[rows:-][0m SELECT * FROM `worlds` LIMIT 1

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[7.635ms] [34;1m[rows:0][0m ALTER TABLE `worlds` ADD `access_settings` text DEFAULT "{}"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[7.032ms] [34;1m[rows:0][0m ALTER TABLE `worlds` ADD `tags` text DEFAULT "[]"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.914ms] [34;1m[rows:0][0m ALTER TABLE `worlds` ADD `time_config` text DEFAULT "{}"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.035ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "table" AND tbl_name = "worlds" AND (sql LIKE "%CONSTRAINT ""fk_users_worlds"" %" OR sql LIKE "%CONSTRAINT fk_users_worlds %" OR sql LIKE "%CONSTRAINT `fk_users_worlds`%" OR sql LIKE "%CONSTRAINT [fk_users_worlds]%" OR sql LIKE "%CONSTRAINT 	fk_users_worlds	%")

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.019ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "index" AND tbl_name = "worlds" AND name = "idx_worlds_creator_id"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.015ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "index" AND tbl_name = "worlds" AND name = "idx_worlds_status"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.011ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "index" AND tbl_name = "worlds" AND name = "idx_worlds_is_public"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.011ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "index" AND tbl_name = "worlds" AND name = "idx_worlds_created_at"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.010ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "index" AND tbl_name = "worlds" AND name = "idx_worlds_deleted_at"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.009ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type='table' AND name="scenes"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.057ms] [34;1m[rows:5][0m SELECT sql FROM sqlite_master WHERE type IN ("table","index") AND tbl_name = "scenes" AND sql IS NOT NULL order by type = "table" desc

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.021ms] [34;1m[rows:-][0m SELECT * FROM `scenes` LIMIT 1

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[5.115ms] [34;1m[rows:0][0m ALTER TABLE `scenes` ADD `tags` text DEFAULT "[]"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.882ms] [34;1m[rows:0][0m ALTER TABLE `scenes` ADD `access_rules` text DEFAULT "{}"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.922ms] [34;1m[rows:0][0m ALTER TABLE `scenes` ADD `connections` text DEFAULT "[]"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.033ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "table" AND tbl_name = "scenes" AND (sql LIKE "%CONSTRAINT ""fk_worlds_scenes"" %" OR sql LIKE "%CONSTRAINT fk_worlds_scenes %" OR sql LIKE "%CONSTRAINT `fk_worlds_scenes`%" OR sql LIKE "%CONSTRAINT [fk_worlds_scenes]%" OR sql LIKE "%CONSTRAINT 	fk_worlds_scenes	%")

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.012ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "index" AND tbl_name = "scenes" AND name = "idx_scenes_world_id"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.013ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "index" AND tbl_name = "scenes" AND name = "idx_scenes_scene_type"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.011ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "index" AND tbl_name = "scenes" AND name = "idx_scenes_status"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.012ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "index" AND tbl_name = "scenes" AND name = "idx_scenes_deleted_at"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.010ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type='table' AND name="characters"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.052ms] [34;1m[rows:7][0m SELECT sql FROM sqlite_master WHERE type IN ("table","index") AND tbl_name = "characters" AND sql IS NOT NULL order by type = "table" desc

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.018ms] [34;1m[rows:-][0m SELECT * FROM `characters` LIMIT 1

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.012ms] [34;1m[rows:1][0m PRAGMA foreign_keys

2025/08/07 09:24:10 [32m
[0m[33m[0.011ms] [34;1m[rows:-][0m SELECT sql FROM sqlite_master WHERE type = "table" AND tbl_name = "characters" AND name = "characters"

2025/08/07 09:24:10 [32m
[0m[33m[0.204ms] [34;1m[rows:0][0m CREATE TABLE `characters__temp`  (`id` text,`world_id` text NOT NULL,`user_id` text,`name` text NOT NULL,`description` text,`character_type` text DEFAULT "player",`current_scene_id` text,`traits` text DEFAULT "[]",`memories` text DEFAULT "{}",`experiences` text DEFAULT "{}",`relationships` text DEFAULT "{}",`status` text DEFAULT "active",`last_action_at` datetime,`created_at` datetime,`updated_at` datetime,`deleted_at` datetime,PRIMARY KEY (`id`),CONSTRAINT `fk_worlds_characters` FOREIGN KEY (`world_id`) REFERENCES `worlds`(`id`),CONSTRAINT `fk_users_characters` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`),CONSTRAINT `fk_scenes_characters` FOREIGN KEY (`current_scene_id`) REFERENCES `scenes`(`id`))

2025/08/07 09:24:10 [32m
[0m[33m[0.023ms] [34;1m[rows:0][0m INSERT INTO `characters__temp`(`id`,`world_id`,`user_id`,`name`,`description`,`character_type`,`current_scene_id`,`traits`,`memories`,`experiences`,`relationships`,`status`,`last_action_at`,`created_at`,`updated_at`,`deleted_at`) SELECT `id`,`world_id`,`user_id`,`name`,`description`,`character_type`,`current_scene_id`,`traits`,`memories`,`experiences`,`relationships`,`status`,`last_action_at`,`created_at`,`updated_at`,`deleted_at` FROM `characters`

2025/08/07 09:24:10 [32m
[0m[33m[0.291ms] [34;1m[rows:0][0m DROP TABLE `characters`

2025/08/07 09:24:10 [32m
[0m[33m[0.428ms] [34;1m[rows:0][0m ALTER TABLE `characters__temp` RENAME TO `characters`

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.889ms] [34;1m[rows:0][0m ALTER TABLE `characters` ADD `characteristics` text DEFAULT "{}"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.889ms] [34;1m[rows:0][0m ALTER TABLE `characters` ADD `is_primary` numeric DEFAULT false

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[7.059ms] [34;1m[rows:0][0m ALTER TABLE `characters` ADD `display_order` integer DEFAULT 0

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[7.076ms] [34;1m[rows:0][0m ALTER TABLE `characters` ADD `last_active_at` datetime

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.037ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "table" AND tbl_name = "characters" AND (sql LIKE "%CONSTRAINT ""fk_worlds_characters"" %" OR sql LIKE "%CONSTRAINT fk_worlds_characters %" OR sql LIKE "%CONSTRAINT `fk_worlds_characters`%" OR sql LIKE "%CONSTRAINT [fk_worlds_characters]%" OR sql LIKE "%CONSTRAINT 	fk_worlds_characters	%")

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.021ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "table" AND tbl_name = "characters" AND (sql LIKE "%CONSTRAINT ""fk_users_characters"" %" OR sql LIKE "%CONSTRAINT fk_users_characters %" OR sql LIKE "%CONSTRAINT `fk_users_characters`%" OR sql LIKE "%CONSTRAINT [fk_users_characters]%" OR sql LIKE "%CONSTRAINT 	fk_users_characters	%")

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.018ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "table" AND tbl_name = "characters" AND (sql LIKE "%CONSTRAINT ""fk_scenes_characters"" %" OR sql LIKE "%CONSTRAINT fk_scenes_characters %" OR sql LIKE "%CONSTRAINT `fk_scenes_characters`%" OR sql LIKE "%CONSTRAINT [fk_scenes_characters]%" OR sql LIKE "%CONSTRAINT 	fk_scenes_characters	%")

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.010ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "index" AND tbl_name = "characters" AND name = "idx_characters_world_id"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.362ms] [34;1m[rows:0][0m CREATE INDEX `idx_characters_world_id` ON `characters`(`world_id`)

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.035ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "index" AND tbl_name = "characters" AND name = "idx_characters_user_id"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.784ms] [34;1m[rows:0][0m CREATE INDEX `idx_characters_user_id` ON `characters`(`user_id`)

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.037ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "index" AND tbl_name = "characters" AND name = "idx_characters_character_type"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.914ms] [34;1m[rows:0][0m CREATE INDEX `idx_characters_character_type` ON `characters`(`character_type`)

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.032ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "index" AND tbl_name = "characters" AND name = "idx_characters_current_scene_id"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.771ms] [34;1m[rows:0][0m CREATE INDEX `idx_characters_current_scene_id` ON `characters`(`current_scene_id`)

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.022ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "index" AND tbl_name = "characters" AND name = "idx_characters_is_primary"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.830ms] [34;1m[rows:0][0m CREATE INDEX `idx_characters_is_primary` ON `characters`(`is_primary`)

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.022ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "index" AND tbl_name = "characters" AND name = "idx_characters_last_active_at"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.829ms] [34;1m[rows:0][0m CREATE INDEX `idx_characters_last_active_at` ON `characters`(`last_active_at`)

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.027ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "index" AND tbl_name = "characters" AND name = "idx_characters_status"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.950ms] [34;1m[rows:0][0m CREATE INDEX `idx_characters_status` ON `characters`(`status`)

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.023ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "index" AND tbl_name = "characters" AND name = "idx_characters_deleted_at"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.804ms] [34;1m[rows:0][0m CREATE INDEX `idx_characters_deleted_at` ON `characters`(`deleted_at`)

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.020ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type='table' AND name="entities"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.067ms] [34;1m[rows:7][0m SELECT sql FROM sqlite_master WHERE type IN ("table","index") AND tbl_name = "entities" AND sql IS NOT NULL order by type = "table" desc

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.022ms] [34;1m[rows:-][0m SELECT * FROM `entities` LIMIT 1

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.542ms] [34;1m[rows:0][0m ALTER TABLE `entities` ADD `container_entity_id` text

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.915ms] [34;1m[rows:0][0m ALTER TABLE `entities` ADD `version` integer NOT NULL DEFAULT 1

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[7.082ms] [34;1m[rows:0][0m ALTER TABLE `entities` ADD `tags` text DEFAULT "[]"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.030ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "table" AND tbl_name = "entities" AND (sql LIKE "%CONSTRAINT ""fk_characters_owned_entities"" %" OR sql LIKE "%CONSTRAINT fk_characters_owned_entities %" OR sql LIKE "%CONSTRAINT `fk_characters_owned_entities`%" OR sql LIKE "%CONSTRAINT [fk_characters_owned_entities]%" OR sql LIKE "%CONSTRAINT 	fk_characters_owned_entities	%")

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.015ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "table" AND tbl_name = "entities" AND (sql LIKE "%CONSTRAINT ""fk_scenes_entities"" %" OR sql LIKE "%CONSTRAINT fk_scenes_entities %" OR sql LIKE "%CONSTRAINT `fk_scenes_entities`%" OR sql LIKE "%CONSTRAINT [fk_scenes_entities]%" OR sql LIKE "%CONSTRAINT 	fk_scenes_entities	%")

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.014ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "table" AND tbl_name = "entities" AND (sql LIKE "%CONSTRAINT ""fk_worlds_entities"" %" OR sql LIKE "%CONSTRAINT fk_worlds_entities %" OR sql LIKE "%CONSTRAINT `fk_worlds_entities`%" OR sql LIKE "%CONSTRAINT [fk_worlds_entities]%" OR sql LIKE "%CONSTRAINT 	fk_worlds_entities	%")

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.013ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "table" AND tbl_name = "entities" AND (sql LIKE "%CONSTRAINT ""fk_entities_contained_entities"" %" OR sql LIKE "%CONSTRAINT fk_entities_contained_entities %" OR sql LIKE "%CONSTRAINT `fk_entities_contained_entities`%" OR sql LIKE "%CONSTRAINT [fk_entities_contained_entities]%" OR sql LIKE "%CONSTRAINT 	fk_entities_contained_entities	%")

2025/08/07 09:24:10 [32m
[0m[33m[0.010ms] [34;1m[rows:-][0m SELECT sql FROM sqlite_master WHERE type = "table" AND tbl_name = "entities" AND name = "entities"

2025/08/07 09:24:10 [32m
[0m[33m[0.279ms] [34;1m[rows:0][0m CREATE TABLE `entities__temp`  (`id` text,`world_id` text NOT NULL,`name` text NOT NULL,`description` text,`entity_type` text NOT NULL,`properties` text DEFAULT "{}",`traits` text DEFAULT "[]",`current_scene_id` text,`owner_id` text,`status` text DEFAULT "active",`created_at` datetime,`updated_at` datetime,`deleted_at` datetime,`container_entity_id` text,`version` integer NOT NULL DEFAULT 1,`tags` text DEFAULT "[]",PRIMARY KEY (`id`),CONSTRAINT `fk_characters_owned_entities` FOREIGN KEY (`owner_id`) REFERENCES `characters`(`id`),CONSTRAINT `fk_scenes_entities` FOREIGN KEY (`current_scene_id`) REFERENCES `scenes`(`id`),CONSTRAINT `fk_worlds_entities` FOREIGN KEY (`world_id`) REFERENCES `worlds`(`id`),CONSTRAINT `fk_entities_contained_entities` FOREIGN KEY (`container_entity_id`) REFERENCES `entities`(`id`))

2025/08/07 09:24:10 [32m
[0m[33m[0.031ms] [34;1m[rows:0][0m INSERT INTO `entities__temp`(`id`,`world_id`,`name`,`description`,`entity_type`,`properties`,`traits`,`current_scene_id`,`owner_id`,`status`,`created_at`,`updated_at`,`deleted_at`,`container_entity_id`,`version`,`tags`) SELECT `id`,`world_id`,`name`,`description`,`entity_type`,`properties`,`traits`,`current_scene_id`,`owner_id`,`status`,`created_at`,`updated_at`,`deleted_at`,`container_entity_id`,`version`,`tags` FROM `entities`

2025/08/07 09:24:10 [32m
[0m[33m[0.348ms] [34;1m[rows:0][0m DROP TABLE `entities`

2025/08/07 09:24:10 [32m
[0m[33m[0.520ms] [34;1m[rows:0][0m ALTER TABLE `entities__temp` RENAME TO `entities`

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.035ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "index" AND tbl_name = "entities" AND name = "idx_entities_world_id"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.765ms] [34;1m[rows:0][0m CREATE INDEX `idx_entities_world_id` ON `entities`(`world_id`)

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.027ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "index" AND tbl_name = "entities" AND name = "idx_entities_entity_type"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.830ms] [34;1m[rows:0][0m CREATE INDEX `idx_entities_entity_type` ON `entities`(`entity_type`)

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.023ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "index" AND tbl_name = "entities" AND name = "idx_entities_container_entity_id"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.961ms] [34;1m[rows:0][0m CREATE INDEX `idx_entities_container_entity_id` ON `entities`(`container_entity_id`)

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.024ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "index" AND tbl_name = "entities" AND name = "idx_entities_current_scene_id"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.812ms] [34;1m[rows:0][0m CREATE INDEX `idx_entities_current_scene_id` ON `entities`(`current_scene_id`)

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.021ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "index" AND tbl_name = "entities" AND name = "idx_entities_owner_id"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.486ms] [34;1m[rows:0][0m CREATE INDEX `idx_entities_owner_id` ON `entities`(`owner_id`)

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.022ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "index" AND tbl_name = "entities" AND name = "idx_entities_status"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.826ms] [34;1m[rows:0][0m CREATE INDEX `idx_entities_status` ON `entities`(`status`)

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.021ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "index" AND tbl_name = "entities" AND name = "idx_entities_deleted_at"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.967ms] [34;1m[rows:0][0m CREATE INDEX `idx_entities_deleted_at` ON `entities`(`deleted_at`)

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.020ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type='table' AND name="events"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.061ms] [34;1m[rows:9][0m SELECT sql FROM sqlite_master WHERE type IN ("table","index") AND tbl_name = "events" AND sql IS NOT NULL order by type = "table" desc

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.022ms] [34;1m[rows:-][0m SELECT * FROM `events` LIMIT 1

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.020ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "table" AND tbl_name = "events" AND (sql LIKE "%CONSTRAINT ""fk_characters_created_events"" %" OR sql LIKE "%CONSTRAINT fk_characters_created_events %" OR sql LIKE "%CONSTRAINT `fk_characters_created_events`%" OR sql LIKE "%CONSTRAINT [fk_characters_created_events]%" OR sql LIKE "%CONSTRAINT 	fk_characters_created_events	%")

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.015ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "table" AND tbl_name = "events" AND (sql LIKE "%CONSTRAINT ""fk_scenes_events"" %" OR sql LIKE "%CONSTRAINT fk_scenes_events %" OR sql LIKE "%CONSTRAINT `fk_scenes_events`%" OR sql LIKE "%CONSTRAINT [fk_scenes_events]%" OR sql LIKE "%CONSTRAINT 	fk_scenes_events	%")

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.013ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "table" AND tbl_name = "events" AND (sql LIKE "%CONSTRAINT ""fk_worlds_events"" %" OR sql LIKE "%CONSTRAINT fk_worlds_events %" OR sql LIKE "%CONSTRAINT `fk_worlds_events`%" OR sql LIKE "%CONSTRAINT [fk_worlds_events]%" OR sql LIKE "%CONSTRAINT 	fk_worlds_events	%")

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.010ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "index" AND tbl_name = "events" AND name = "idx_events_world_id"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.010ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "index" AND tbl_name = "events" AND name = "idx_events_scene_id"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.010ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "index" AND tbl_name = "events" AND name = "idx_events_creator_id"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.019ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "index" AND tbl_name = "events" AND name = "idx_events_event_type"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.010ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "index" AND tbl_name = "events" AND name = "idx_events_status"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.010ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "index" AND tbl_name = "events" AND name = "idx_events_priority"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.010ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "index" AND tbl_name = "events" AND name = "idx_events_scheduled_at"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.010ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "index" AND tbl_name = "events" AND name = "idx_events_deleted_at"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.010ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type='table' AND name="ai_interactions"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.042ms] [34;1m[rows:6][0m SELECT sql FROM sqlite_master WHERE type IN ("table","index") AND tbl_name = "ai_interactions" AND sql IS NOT NULL order by type = "table" desc

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.015ms] [34;1m[rows:-][0m SELECT * FROM `ai_interactions` LIMIT 1

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.017ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "table" AND tbl_name = "ai_interactions" AND (sql LIKE "%CONSTRAINT ""fk_ai_interactions_world"" %" OR sql LIKE "%CONSTRAINT fk_ai_interactions_world %" OR sql LIKE "%CONSTRAINT `fk_ai_interactions_world`%" OR sql LIKE "%CONSTRAINT [fk_ai_interactions_world]%" OR sql LIKE "%CONSTRAINT 	fk_ai_interactions_world	%")

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.013ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "table" AND tbl_name = "ai_interactions" AND (sql LIKE "%CONSTRAINT ""fk_ai_interactions_user"" %" OR sql LIKE "%CONSTRAINT fk_ai_interactions_user %" OR sql LIKE "%CONSTRAINT `fk_ai_interactions_user`%" OR sql LIKE "%CONSTRAINT [fk_ai_interactions_user]%" OR sql LIKE "%CONSTRAINT 	fk_ai_interactions_user	%")

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.009ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "index" AND tbl_name = "ai_interactions" AND name = "idx_ai_interactions_world_id"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.011ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "index" AND tbl_name = "ai_interactions" AND name = "idx_ai_interactions_user_id"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.009ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "index" AND tbl_name = "ai_interactions" AND name = "idx_ai_interactions_interaction_type"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.009ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "index" AND tbl_name = "ai_interactions" AND name = "idx_ai_interactions_status"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.010ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type = "index" AND tbl_name = "ai_interactions" AND name = "idx_ai_interactions_created_at"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:174
[0m[33m[0.009ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type='table' AND name="users"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:149
[0m[33m[7.035ms] [34;1m[rows:0][0m 
CREATE TRIGGER update_users_updated_at 
    AFTER UPDATE ON users 
    FOR EACH ROW 
    BEGIN 
        UPDATE users SET updated_at = datetime('now') WHERE id = NEW.id;
    END;

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:174
[0m[33m[0.017ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type='table' AND name="user_stats"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:149
[0m[33m[6.863ms] [34;1m[rows:0][0m 
CREATE TRIGGER update_user_stats_updated_at 
    AFTER UPDATE ON user_stats 
    FOR EACH ROW 
    BEGIN 
        UPDATE user_stats SET updated_at = datetime('now') WHERE id = NEW.id;
    END;

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:174
[0m[33m[0.017ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type='table' AND name="worlds"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:149
[0m[33m[7.038ms] [34;1m[rows:0][0m 
CREATE TRIGGER update_worlds_updated_at 
    AFTER UPDATE ON worlds 
    FOR EACH ROW 
    BEGIN 
        UPDATE worlds SET updated_at = datetime('now') WHERE id = NEW.id;
    END;

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:174
[0m[33m[0.019ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type='table' AND name="characters"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:149
[0m[33m[6.864ms] [34;1m[rows:0][0m 
CREATE TRIGGER update_characters_updated_at 
    AFTER UPDATE ON characters 
    FOR EACH ROW 
    BEGIN 
        UPDATE characters SET updated_at = datetime('now') WHERE id = NEW.id;
    END;

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:174
[0m[33m[0.028ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type='table' AND name="scenes"

2025/08/07 09:24:10 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:149
[0m[33m[6.853ms] [34;1m[rows:0][0m 
CREATE TRIGGER update_scenes_updated_at 
    AFTER UPDATE ON scenes 
    FOR EACH ROW 
    BEGIN 
        UPDATE scenes SET updated_at = datetime('now') WHERE id = NEW.id;
    END;
2025/08/07 09:24:10 logger.go:83: [INFO] 开发环境数据库迁移成功
2025/08/07 09:24:10 logger.go:83: [INFO] 数据库迁移完成
[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

2025/08/07 09:24:10 logger.go:83: [INFO] 异步任务管理器已启动 worker_count=5
2025/08/07 09:24:10 logger.go:83: [INFO] 数据流处理器已启动
[GIN-debug] GET    /health                   --> ai-text-game-iam-npc/internal/routes.SetupRoutes.func1 (6 handlers)
[GIN-debug] GET    /api/health               --> ai-text-game-iam-npc/internal/routes.SetupRoutes.func1 (6 handlers)
[GIN-debug] GET    /api/v1/auth/:provider/url --> ai-text-game-iam-npc/internal/handlers.(*AuthHandler).GetAuthURL-fm (6 handlers)
[GIN-debug] GET    /api/v1/auth/:provider/callback --> ai-text-game-iam-npc/internal/handlers.(*AuthHandler).HandleCallback-fm (6 handlers)
[GIN-debug] POST   /api/v1/auth/refresh      --> ai-text-game-iam-npc/internal/handlers.(*AuthHandler).RefreshToken-fm (6 handlers)
[GIN-debug] GET    /api/v1/validation/config --> ai-text-game-iam-npc/internal/handlers.(*ValidationHandler).GetValidationConfig-fm (6 handlers)
[GIN-debug] GET    /api/v1/user/profile      --> ai-text-game-iam-npc/internal/handlers.(*AuthHandler).GetProfile-fm (8 handlers)
[GIN-debug] PUT    /api/v1/user/profile      --> ai-text-game-iam-npc/internal/handlers.(*AuthHandler).UpdateProfile-fm (8 handlers)
[GIN-debug] POST   /api/v1/validation/validate --> ai-text-game-iam-npc/internal/handlers.(*ValidationHandler).ValidateContent-fm (8 handlers)
[GIN-debug] POST   /api/v1/validation/batch-validate --> ai-text-game-iam-npc/internal/handlers.(*ValidationHandler).BatchValidateContent-fm (8 handlers)
[GIN-debug] GET    /api/v1/validation/stats  --> ai-text-game-iam-npc/internal/handlers.(*ValidationHandler).GetValidationStats-fm (8 handlers)
[GIN-debug] POST   /api/v1/ai/generate       --> ai-text-game-iam-npc/internal/handlers.(*AIHandler).GenerateContent-fm (8 handlers)
[GIN-debug] POST   /api/v1/ai/generate/scene --> ai-text-game-iam-npc/internal/handlers.(*AIHandler).GenerateScene-fm (8 handlers)
[GIN-debug] POST   /api/v1/ai/generate/character --> ai-text-game-iam-npc/internal/handlers.(*AIHandler).GenerateCharacter-fm (8 handlers)
[GIN-debug] POST   /api/v1/ai/generate/event --> ai-text-game-iam-npc/internal/handlers.(*AIHandler).GenerateEvent-fm (8 handlers)
[GIN-debug] GET    /api/v1/ai/history        --> ai-text-game-iam-npc/internal/handlers.(*AIHandler).GetInteractionHistory-fm (8 handlers)
[GIN-debug] GET    /api/v1/ai/stats          --> ai-text-game-iam-npc/internal/handlers.(*AIHandler).GetTokenUsageStats-fm (8 handlers)
[GIN-debug] POST   /api/v1/game/worlds       --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).CreateWorld-fm (8 handlers)
[GIN-debug] GET    /api/v1/game/worlds/:world_id --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).GetWorld-fm (8 handlers)
[GIN-debug] PUT    /api/v1/game/worlds/:world_id --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).UpdateWorld-fm (8 handlers)
[GIN-debug] DELETE /api/v1/game/worlds/:world_id --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).DeleteWorld-fm (8 handlers)
[GIN-debug] POST   /api/v1/game/worlds/:world_id/join --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).JoinWorld-fm (8 handlers)
[GIN-debug] POST   /api/v1/game/worlds/:world_id/leave --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).LeaveWorld-fm (8 handlers)
[GIN-debug] GET    /api/v1/game/worlds/:world_id/characters --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).GetWorldCharacters-fm (8 handlers)
[GIN-debug] GET    /api/v1/game/worlds/:world_id/state --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).GetWorldState-fm (8 handlers)
[GIN-debug] POST   /api/v1/game/worlds/:world_id/time --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).UpdateWorldTime-fm (8 handlers)
[GIN-debug] POST   /api/v1/game/worlds/:world_id/tick --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).ProcessWorldTick-fm (8 handlers)
[GIN-debug] GET    /api/v1/game/my-worlds    --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).GetMyWorlds-fm (8 handlers)
[GIN-debug] GET    /api/v1/game/public-worlds --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).GetPublicWorlds-fm (8 handlers)
[GIN-debug] POST   /api/v1/game/characters   --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).CreateCharacter-fm (8 handlers)
[GIN-debug] GET    /api/v1/game/characters/:character_id --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).GetCharacter-fm (8 handlers)
[GIN-debug] PUT    /api/v1/game/characters/:character_id --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).UpdateCharacter-fm (8 handlers)
[GIN-debug] DELETE /api/v1/game/characters/:character_id --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).DeleteCharacter-fm (8 handlers)
[GIN-debug] POST   /api/v1/game/characters/:character_id/move --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).MoveCharacter-fm (8 handlers)
[GIN-debug] POST   /api/v1/game/characters/:character_id/traits --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).AddCharacterTrait-fm (8 handlers)
[GIN-debug] POST   /api/v1/game/characters/:character_id/memories --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).AddCharacterMemory-fm (8 handlers)
[GIN-debug] POST   /api/v1/game/characters/:character_id/experiences --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).AddCharacterExperience-fm (8 handlers)
[GIN-debug] POST   /api/v1/game/characters/:character_id/actions --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).PerformAction-fm (8 handlers)
[GIN-debug] POST   /api/v1/game/characters/:character_id/interact/:target_character_id --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).InteractWithCharacter-fm (8 handlers)
[GIN-debug] POST   /api/v1/game/characters/:character_id/speak --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).SpeakInScene-fm (8 handlers)
[GIN-debug] GET    /api/v1/game/my-characters --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).GetMyCharacters-fm (8 handlers)
[GIN-debug] POST   /api/v1/game/scenes       --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).CreateScene-fm (8 handlers)
[GIN-debug] GET    /api/v1/game/scenes/:scene_id --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).GetScene-fm (8 handlers)
[GIN-debug] POST   /api/v1/game/events       --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).CreateEvent-fm (8 handlers)
[GIN-debug] POST   /api/v1/game/events/:event_id/process --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).ProcessEvent-fm (8 handlers)
[GIN-debug] POST   /api/v1/game/events/trigger --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).TriggerEvent-fm (8 handlers)
[GIN-debug] GET    /api/v1/admin/stats       --> ai-text-game-iam-npc/internal/routes.SetupRoutes.func2 (7 handlers)
[GIN-debug] GET    /api/v1/admin/users       --> ai-text-game-iam-npc/internal/routes.SetupRoutes.func3 (7 handlers)
[GIN-debug] GET    /api/v1/admin/worlds      --> ai-text-game-iam-npc/internal/routes.SetupRoutes.func4 (7 handlers)
[GIN-debug] GET    /static/*filepath         --> github.com/gin-gonic/gin.(*RouterGroup).createStaticHandler.func1 (6 handlers)
[GIN-debug] HEAD   /static/*filepath         --> github.com/gin-gonic/gin.(*RouterGroup).createStaticHandler.func1 (6 handlers)
[GIN-debug] GET    /favicon.ico              --> github.com/gin-gonic/gin.(*RouterGroup).StaticFile.func1 (6 handlers)
[GIN-debug] HEAD   /favicon.ico              --> github.com/gin-gonic/gin.(*RouterGroup).StaticFile.func1 (6 handlers)
[GIN-debug] GET    /assets/*filepath         --> github.com/gin-gonic/gin.(*RouterGroup).createStaticHandler.func1 (6 handlers)
[GIN-debug] HEAD   /assets/*filepath         --> github.com/gin-gonic/gin.(*RouterGroup).createStaticHandler.func1 (6 handlers)
[GIN-debug] GET    /vite.svg                 --> github.com/gin-gonic/gin.(*RouterGroup).StaticFile.func1 (6 handlers)
[GIN-debug] HEAD   /vite.svg                 --> github.com/gin-gonic/gin.(*RouterGroup).StaticFile.func1 (6 handlers)
2025/08/07 09:24:10 logger.go:83: [INFO] 服务器启动 port=8080 environment=development
2025/08/07 09:24:10 logger.go:103: [FATAL] 服务器启动失败 error=listen tcp: address tcp/%!s(int=8080): unknown port
