package models

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestTimeScheduleConfig_Validate 测试时间段配置验证
func TestTimeScheduleConfig_Validate(t *testing.T) {
	tests := []struct {
		name    string
		config  *TimeScheduleConfig
		wantErr bool
		errMsg  string
	}{
		{
			name: "有效的基本配置",
			config: &TimeScheduleConfig{
				Enabled:     true,
				Timezone:    "Asia/Shanghai",
				DefaultRate: 1.0,
				Schedules: []TimeSchedule{
					{
						Name:      "测试时间段",
						StartTime: "08:00",
						EndTime:   "18:00",
						TimeRate:  2.0,
						Enabled:   true,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "禁用的配置不需要验证",
			config: &TimeScheduleConfig{
				Enabled:     false,
				DefaultRate: 1.0,
			},
			wantErr: false,
		},
		{
			name: "无效的时区",
			config: &TimeScheduleConfig{
				Enabled:     true,
				Timezone:    "Invalid/Timezone",
				DefaultRate: 1.0,
				Schedules: []TimeSchedule{
					{
						Name:      "测试时间段",
						StartTime: "08:00",
						EndTime:   "18:00",
						TimeRate:  2.0,
						Enabled:   true,
					},
				},
			},
			wantErr: true,
			errMsg:  "无效的时区设置",
		},
		{
			name: "默认速率为零",
			config: &TimeScheduleConfig{
				Enabled:     true,
				Timezone:    "Asia/Shanghai",
				DefaultRate: 0,
				Schedules: []TimeSchedule{
					{
						Name:      "测试时间段",
						StartTime: "08:00",
						EndTime:   "18:00",
						TimeRate:  2.0,
						Enabled:   true,
					},
				},
			},
			wantErr: true,
			errMsg:  "默认时间速率必须大于0",
		},
		{
			name: "没有时间段",
			config: &TimeScheduleConfig{
				Enabled:     true,
				Timezone:    "Asia/Shanghai",
				DefaultRate: 1.0,
				Schedules:   []TimeSchedule{},
			},
			wantErr: true,
			errMsg:  "启用时间段配置时必须至少定义一个时间段",
		},
		{
			name: "时间段重叠",
			config: &TimeScheduleConfig{
				Enabled:     true,
				Timezone:    "Asia/Shanghai",
				DefaultRate: 1.0,
				Schedules: []TimeSchedule{
					{
						Name:      "时间段1",
						StartTime: "08:00",
						EndTime:   "12:00",
						TimeRate:  2.0,
						Enabled:   true,
					},
					{
						Name:      "时间段2",
						StartTime: "10:00",
						EndTime:   "14:00",
						TimeRate:  3.0,
						Enabled:   true,
					},
				},
			},
			wantErr: true,
			errMsg:  "时间段重叠",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.Validate()
			if tt.wantErr {
				assert.Error(t, err)
				if tt.errMsg != "" {
					assert.Contains(t, err.Error(), tt.errMsg)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestTimeSchedule_Validate 测试单个时间段验证
func TestTimeSchedule_Validate(t *testing.T) {
	tests := []struct {
		name     string
		schedule TimeSchedule
		wantErr  bool
		errMsg   string
	}{
		{
			name: "有效的时间段",
			schedule: TimeSchedule{
				Name:      "测试时间段",
				StartTime: "08:00",
				EndTime:   "18:00",
				TimeRate:  2.0,
				Enabled:   true,
			},
			wantErr: false,
		},
		{
			name: "禁用的时间段不需要验证",
			schedule: TimeSchedule{
				Name:    "禁用时间段",
				Enabled: false,
			},
			wantErr: false,
		},
		{
			name: "空名称",
			schedule: TimeSchedule{
				Name:      "",
				StartTime: "08:00",
				EndTime:   "18:00",
				TimeRate:  2.0,
				Enabled:   true,
			},
			wantErr: true,
			errMsg:  "时间段名称不能为空",
		},
		{
			name: "无效的开始时间格式",
			schedule: TimeSchedule{
				Name:      "测试时间段",
				StartTime: "8:00",
				EndTime:   "18:00",
				TimeRate:  2.0,
				Enabled:   true,
			},
			wantErr: true,
			errMsg:  "开始时间格式错误",
		},
		{
			name: "无效的时间速率",
			schedule: TimeSchedule{
				Name:      "测试时间段",
				StartTime: "08:00",
				EndTime:   "18:00",
				TimeRate:  0,
				Enabled:   true,
			},
			wantErr: true,
			errMsg:  "时间速率必须大于0",
		},
		{
			name: "无效的星期值",
			schedule: TimeSchedule{
				Name:      "测试时间段",
				StartTime: "08:00",
				EndTime:   "18:00",
				TimeRate:  2.0,
				Enabled:   true,
				Weekdays:  []int{0, 8}, // 无效的星期值
			},
			wantErr: true,
			errMsg:  "星期值必须在1-7之间",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.schedule.Validate()
			if tt.wantErr {
				assert.Error(t, err)
				if tt.errMsg != "" {
					assert.Contains(t, err.Error(), tt.errMsg)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestTimeScheduleConfig_GetCurrentTimeRate 测试获取当前时间速率
func TestTimeScheduleConfig_GetCurrentTimeRate(t *testing.T) {
	// 创建测试配置
	config := &TimeScheduleConfig{
		Enabled:     true,
		Timezone:    "Asia/Shanghai",
		DefaultRate: 1.0,
		Schedules: []TimeSchedule{
			{
				Name:      "早晨",
				StartTime: "06:00",
				EndTime:   "12:00",
				TimeRate:  2.0,
				Enabled:   true,
			},
			{
				Name:      "下午",
				StartTime: "12:00",
				EndTime:   "18:00",
				TimeRate:  3.0,
				Enabled:   true,
			},
			{
				Name:      "晚上",
				StartTime: "18:00",
				EndTime:   "24:00",
				TimeRate:  1.5,
				Enabled:   true,
			},
		},
	}

	// 测试获取当前时间速率
	rate := config.GetCurrentTimeRate()
	assert.Greater(t, rate, 0.0, "时间速率应该大于0")

	// 测试禁用配置
	config.Enabled = false
	rate = config.GetCurrentTimeRate()
	assert.Equal(t, config.DefaultRate, rate, "禁用时应返回默认速率")
}

// TestIsTimeInRange 测试时间范围检查
func TestIsTimeInRange(t *testing.T) {
	tests := []struct {
		name        string
		currentTime string
		startTime   string
		endTime     string
		expected    bool
	}{
		{
			name:        "在范围内",
			currentTime: "10:00",
			startTime:   "08:00",
			endTime:     "12:00",
			expected:    true,
		},
		{
			name:        "在范围外",
			currentTime: "14:00",
			startTime:   "08:00",
			endTime:     "12:00",
			expected:    false,
		},
		{
			name:        "跨天范围内",
			currentTime: "02:00",
			startTime:   "22:00",
			endTime:     "06:00",
			expected:    true,
		},
		{
			name:        "跨天范围外",
			currentTime: "10:00",
			startTime:   "22:00",
			endTime:     "06:00",
			expected:    false,
		},
		{
			name:        "边界值-开始时间",
			currentTime: "08:00",
			startTime:   "08:00",
			endTime:     "12:00",
			expected:    true,
		},
		{
			name:        "边界值-结束时间",
			currentTime: "12:00",
			startTime:   "08:00",
			endTime:     "12:00",
			expected:    true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isTimeInRange(tt.currentTime, tt.startTime, tt.endTime)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestTimeToMinutes 测试时间转换为分钟数
func TestTimeToMinutes(t *testing.T) {
	tests := []struct {
		timeStr  string
		expected int
	}{
		{"00:00", 0},
		{"01:00", 60},
		{"12:30", 750},
		{"23:59", 1439},
	}

	for _, tt := range tests {
		t.Run(tt.timeStr, func(t *testing.T) {
			result := timeToMinutes(tt.timeStr)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestValidateTimeFormat 测试时间格式验证
func TestValidateTimeFormat(t *testing.T) {
	tests := []struct {
		timeStr string
		wantErr bool
	}{
		{"08:00", false},
		{"23:59", false},
		{"00:00", false},
		{"8:00", true},  // 缺少前导零
		{"08:60", true}, // 无效分钟
		{"24:00", true}, // 无效小时
		{"08", true},    // 格式错误
		{"08:00:00", true}, // 包含秒
	}

	for _, tt := range tests {
		t.Run(tt.timeStr, func(t *testing.T) {
			err := validateTimeFormat(tt.timeStr)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestCreateDefaultTimeScheduleConfig 测试创建默认配置
func TestCreateDefaultTimeScheduleConfig(t *testing.T) {
	config := CreateDefaultTimeScheduleConfig()
	
	require.NotNil(t, config)
	assert.False(t, config.Enabled, "默认配置应该是禁用的")
	assert.Equal(t, "Asia/Shanghai", config.Timezone)
	assert.Equal(t, 1.0, config.DefaultRate)
	assert.Greater(t, len(config.Schedules), 0, "应该有默认的时间段")
	
	// 验证默认配置的有效性
	config.Enabled = true // 启用以进行验证
	err := config.Validate()
	assert.NoError(t, err, "默认配置应该是有效的")
}

// TestHasCommonWeekdays 测试星期过滤器交集检查
func TestHasCommonWeekdays(t *testing.T) {
	tests := []struct {
		name      string
		weekdays1 []int
		weekdays2 []int
		expected  bool
	}{
		{
			name:      "有交集",
			weekdays1: []int{1, 2, 3},
			weekdays2: []int{3, 4, 5},
			expected:  true,
		},
		{
			name:      "无交集",
			weekdays1: []int{1, 2},
			weekdays2: []int{6, 7},
			expected:  false,
		},
		{
			name:      "第一个为空",
			weekdays1: []int{},
			weekdays2: []int{1, 2, 3},
			expected:  true,
		},
		{
			name:      "第二个为空",
			weekdays1: []int{1, 2, 3},
			weekdays2: []int{},
			expected:  true,
		},
		{
			name:      "都为空",
			weekdays1: []int{},
			weekdays2: []int{},
			expected:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := hasCommonWeekdays(tt.weekdays1, tt.weekdays2)
			assert.Equal(t, tt.expected, result)
		})
	}
}
