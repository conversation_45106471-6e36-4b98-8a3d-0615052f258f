package models

import (
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"
)

// TimeScheduleConfig 时间段配置结构
// 用于定义基于现实时间段的动态游戏时间速率控制
type TimeScheduleConfig struct {
	// 是否启用时间段配置
	Enabled bool `json:"enabled"`
	
	// 时区设置，默认为 "Asia/Shanghai"
	Timezone string `json:"timezone"`
	
	// 时间段列表，按开始时间排序
	Schedules []TimeSchedule `json:"schedules"`
	
	// 默认时间速率（当没有匹配的时间段时使用）
	DefaultRate float64 `json:"default_rate"`
	
	// 配置版本号，用于热更新检测
	Version int64 `json:"version"`
	
	// 最后更新时间
	LastUpdated time.Time `json:"last_updated"`
}

// TimeSchedule 单个时间段配置
type TimeSchedule struct {
	// 时间段名称（用于标识和日志）
	Name string `json:"name"`
	
	// 开始时间（24小时制，格式：HH:MM）
	StartTime string `json:"start_time"`
	
	// 结束时间（24小时制，格式：HH:MM）
	EndTime string `json:"end_time"`
	
	// 该时间段的游戏时间速率倍数
	TimeRate float64 `json:"time_rate"`
	
	// 是否启用该时间段
	Enabled bool `json:"enabled"`
	
	// 描述信息
	Description string `json:"description,omitempty"`
	
	// 星期过滤器（可选）：1-7 表示周一到周日，空表示每天都生效
	Weekdays []int `json:"weekdays,omitempty"`
}

// Validate 验证时间段配置的有效性
func (tsc *TimeScheduleConfig) Validate() error {
	if !tsc.Enabled {
		return nil // 未启用时不需要验证
	}
	
	// 验证时区
	if tsc.Timezone == "" {
		tsc.Timezone = "Asia/Shanghai" // 设置默认时区
	}
	
	if _, err := time.LoadLocation(tsc.Timezone); err != nil {
		return fmt.Errorf("无效的时区设置: %s", tsc.Timezone)
	}
	
	// 验证默认速率
	if tsc.DefaultRate <= 0 {
		return fmt.Errorf("默认时间速率必须大于0，当前值: %f", tsc.DefaultRate)
	}
	
	// 验证时间段配置
	if len(tsc.Schedules) == 0 {
		return fmt.Errorf("启用时间段配置时必须至少定义一个时间段")
	}
	
	// 验证每个时间段
	for i, schedule := range tsc.Schedules {
		if err := schedule.Validate(); err != nil {
			return fmt.Errorf("时间段 %d (%s) 配置错误: %w", i, schedule.Name, err)
		}
	}
	
	// 检查时间段重叠
	if err := tsc.checkOverlaps(); err != nil {
		return fmt.Errorf("时间段重叠检查失败: %w", err)
	}
	
	return nil
}

// Validate 验证单个时间段配置
func (ts *TimeSchedule) Validate() error {
	if !ts.Enabled {
		return nil // 未启用的时间段不需要验证
	}
	
	// 验证名称
	if strings.TrimSpace(ts.Name) == "" {
		return fmt.Errorf("时间段名称不能为空")
	}
	
	// 验证时间格式
	if err := validateTimeFormat(ts.StartTime); err != nil {
		return fmt.Errorf("开始时间格式错误: %w", err)
	}
	
	if err := validateTimeFormat(ts.EndTime); err != nil {
		return fmt.Errorf("结束时间格式错误: %w", err)
	}
	
	// 验证时间速率
	if ts.TimeRate <= 0 {
		return fmt.Errorf("时间速率必须大于0，当前值: %f", ts.TimeRate)
	}
	
	// 验证星期过滤器
	for _, weekday := range ts.Weekdays {
		if weekday < 1 || weekday > 7 {
			return fmt.Errorf("星期值必须在1-7之间，当前值: %d", weekday)
		}
	}
	
	return nil
}

// validateTimeFormat 验证时间格式（HH:MM）
func validateTimeFormat(timeStr string) error {
	parts := strings.Split(timeStr, ":")
	if len(parts) != 2 {
		return fmt.Errorf("时间格式必须为 HH:MM，当前值: %s", timeStr)
	}

	// 检查小时部分格式（必须是两位数）
	if len(parts[0]) != 2 {
		return fmt.Errorf("小时必须是两位数格式，当前值: %s", parts[0])
	}

	// 检查分钟部分格式（必须是两位数）
	if len(parts[1]) != 2 {
		return fmt.Errorf("分钟必须是两位数格式，当前值: %s", parts[1])
	}

	hour, err := strconv.Atoi(parts[0])
	if err != nil || hour < 0 || hour > 23 {
		return fmt.Errorf("小时值必须在0-23之间，当前值: %s", parts[0])
	}

	minute, err := strconv.Atoi(parts[1])
	if err != nil || minute < 0 || minute > 59 {
		return fmt.Errorf("分钟值必须在0-59之间，当前值: %s", parts[1])
	}

	return nil
}

// checkOverlaps 检查时间段是否有重叠
func (tsc *TimeScheduleConfig) checkOverlaps() error {
	enabledSchedules := make([]TimeSchedule, 0)
	
	// 只检查启用的时间段
	for _, schedule := range tsc.Schedules {
		if schedule.Enabled {
			enabledSchedules = append(enabledSchedules, schedule)
		}
	}
	
	if len(enabledSchedules) <= 1 {
		return nil // 只有一个或没有启用的时间段，不会重叠
	}
	
	// 按开始时间排序
	sort.Slice(enabledSchedules, func(i, j int) bool {
		return timeToMinutes(enabledSchedules[i].StartTime) < timeToMinutes(enabledSchedules[j].StartTime)
	})
	
	// 检查相邻时间段是否重叠
	for i := 0; i < len(enabledSchedules)-1; i++ {
		current := enabledSchedules[i]
		next := enabledSchedules[i+1]
		
		// 检查是否有相同的星期过滤器
		if hasCommonWeekdays(current.Weekdays, next.Weekdays) {
			currentEnd := timeToMinutes(current.EndTime)
			nextStart := timeToMinutes(next.StartTime)
			
			// 处理跨天的情况
			if currentEnd <= timeToMinutes(current.StartTime) {
				currentEnd += 24 * 60 // 加一天
			}
			
			if currentEnd > nextStart {
				return fmt.Errorf("时间段 '%s' (%s-%s) 与 '%s' (%s-%s) 存在重叠",
					current.Name, current.StartTime, current.EndTime,
					next.Name, next.StartTime, next.EndTime)
			}
		}
	}
	
	return nil
}

// timeToMinutes 将时间字符串转换为分钟数（从00:00开始计算）
func timeToMinutes(timeStr string) int {
	parts := strings.Split(timeStr, ":")
	hour, _ := strconv.Atoi(parts[0])
	minute, _ := strconv.Atoi(parts[1])
	return hour*60 + minute
}

// hasCommonWeekdays 检查两个星期过滤器是否有交集
func hasCommonWeekdays(weekdays1, weekdays2 []int) bool {
	// 如果任一为空，表示每天都生效，则有交集
	if len(weekdays1) == 0 || len(weekdays2) == 0 {
		return true
	}
	
	// 检查是否有相同的星期
	for _, w1 := range weekdays1 {
		for _, w2 := range weekdays2 {
			if w1 == w2 {
				return true
			}
		}
	}
	
	return false
}

// GetCurrentTimeRate 根据当前时间获取应该使用的时间速率
func (tsc *TimeScheduleConfig) GetCurrentTimeRate() float64 {
	if !tsc.Enabled {
		return tsc.DefaultRate
	}
	
	// 获取当前时间（使用配置的时区）
	loc, err := time.LoadLocation(tsc.Timezone)
	if err != nil {
		// 时区加载失败，使用默认速率
		return tsc.DefaultRate
	}
	
	now := time.Now().In(loc)
	currentTime := fmt.Sprintf("%02d:%02d", now.Hour(), now.Minute())
	currentWeekday := int(now.Weekday())
	if currentWeekday == 0 {
		currentWeekday = 7 // 将周日从0改为7
	}
	
	// 查找匹配的时间段
	for _, schedule := range tsc.Schedules {
		if !schedule.Enabled {
			continue
		}
		
		// 检查星期过滤器
		if len(schedule.Weekdays) > 0 {
			found := false
			for _, weekday := range schedule.Weekdays {
				if weekday == currentWeekday {
					found = true
					break
				}
			}
			if !found {
				continue
			}
		}
		
		// 检查时间范围
		if isTimeInRange(currentTime, schedule.StartTime, schedule.EndTime) {
			return schedule.TimeRate
		}
	}
	
	// 没有匹配的时间段，使用默认速率
	return tsc.DefaultRate
}

// isTimeInRange 检查当前时间是否在指定的时间范围内
func isTimeInRange(currentTime, startTime, endTime string) bool {
	current := timeToMinutes(currentTime)
	start := timeToMinutes(startTime)
	end := timeToMinutes(endTime)
	
	// 处理跨天的情况（如 22:00-06:00）
	if start > end {
		// 跨天情况：当前时间在开始时间之后或结束时间之前
		return current >= start || current <= end
	} else {
		// 同一天情况：当前时间在开始和结束时间之间
		return current >= start && current <= end
	}
}

// GetActiveSchedule 获取当前生效的时间段配置
func (tsc *TimeScheduleConfig) GetActiveSchedule() *TimeSchedule {
	if !tsc.Enabled {
		return nil
	}
	
	// 获取当前时间（使用配置的时区）
	loc, err := time.LoadLocation(tsc.Timezone)
	if err != nil {
		return nil
	}
	
	now := time.Now().In(loc)
	currentTime := fmt.Sprintf("%02d:%02d", now.Hour(), now.Minute())
	currentWeekday := int(now.Weekday())
	if currentWeekday == 0 {
		currentWeekday = 7 // 将周日从0改为7
	}
	
	// 查找匹配的时间段
	for _, schedule := range tsc.Schedules {
		if !schedule.Enabled {
			continue
		}
		
		// 检查星期过滤器
		if len(schedule.Weekdays) > 0 {
			found := false
			for _, weekday := range schedule.Weekdays {
				if weekday == currentWeekday {
					found = true
					break
				}
			}
			if !found {
				continue
			}
		}
		
		// 检查时间范围
		if isTimeInRange(currentTime, schedule.StartTime, schedule.EndTime) {
			// 返回副本以避免外部修改
			scheduleCopy := schedule
			return &scheduleCopy
		}
	}
	
	return nil
}

// CreateDefaultConfig 创建默认的时间段配置
func CreateDefaultTimeScheduleConfig() *TimeScheduleConfig {
	return &TimeScheduleConfig{
		Enabled:     false, // 默认不启用
		Timezone:    "Asia/Shanghai",
		DefaultRate: 1.0,
		Version:     1,
		LastUpdated: time.Now(),
		Schedules: []TimeSchedule{
			{
				Name:        "深夜低速",
				StartTime:   "00:00",
				EndTime:     "08:00",
				TimeRate:    1.0,
				Enabled:     true,
				Description: "凌晨时段，玩家较少，使用正常速率",
			},
			{
				Name:        "白天高速",
				StartTime:   "08:00",
				EndTime:     "18:00",
				TimeRate:    5.0,
				Enabled:     true,
				Description: "白天时段，玩家活跃，加快游戏进度",
			},
			{
				Name:        "晚上中速",
				StartTime:   "18:00",
				EndTime:     "24:00",
				TimeRate:    2.0,
				Enabled:     true,
				Description: "晚上时段，玩家次活跃，中等速率",
			},
		},
	}
}
