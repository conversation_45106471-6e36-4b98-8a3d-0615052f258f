package game

import (
	"context"
	"fmt"
	"testing"
	"time"

	"ai-text-game-iam-npc/internal/models"
	"ai-text-game-iam-npc/pkg/logger"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// setupTestDB 设置测试数据库
func setupTestDB(t *testing.T) *gorm.DB {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	require.NoError(t, err)

	// 自动迁移
	err = db.AutoMigrate(
		&models.User{},
		&models.World{},
		&models.Scene{},
		&models.Character{},
		&models.Entity{},
		&models.Event{},
	)
	require.NoError(t, err)

	return db
}

// createTestWorld 创建测试世界
func createTestWorld(t *testing.T, db *gorm.DB, worldID string) *models.World {
	world := &models.World{
		ID:        worldID,
		Name:      "测试世界",
		CreatorID: "test-user-001",
		Status:    "active",
		WorldConfig: models.JSON{
			"time_rate":     2.0,
			"tick_interval": 30,
		},
		WorldState: models.JSON{
			"current_tick": 0,
			"last_tick_at": time.Now(),
		},
		TimeConfig: models.JSON{
			"time_multiplier": 1.0,
		},
		GameTime:       0,
		CurrentPlayers: 1,
	}

	err := db.Create(world).Error
	require.NoError(t, err)

	return world
}

// TestDynamicTimeManager_NewDynamicTimeManager 测试创建动态时间管理器
func TestDynamicTimeManager_NewDynamicTimeManager(t *testing.T) {
	db := setupTestDB(t)
	logger := logger.New("debug")

	manager := NewDynamicTimeManager(db, logger)

	assert.NotNil(t, manager)
	assert.Equal(t, db, manager.db)
	assert.Equal(t, logger, manager.logger)
	assert.NotNil(t, manager.worldStates)
	assert.Equal(t, 30*time.Second, manager.checkInterval)
	assert.Equal(t, 50, manager.batchUpdateSize)
	assert.Equal(t, 3, manager.maxRetries)
	assert.False(t, manager.isRunning)
}

// TestDynamicTimeManager_AddWorld 测试添加世界
func TestDynamicTimeManager_AddWorld(t *testing.T) {
	db := setupTestDB(t)
	logger := logger.New("debug")
	manager := NewDynamicTimeManager(db, logger)

	// 创建测试世界
	world := createTestWorld(t, db, "test-world-001")

	// 添加世界到管理器
	err := manager.AddWorld(context.Background(), world.ID)
	assert.NoError(t, err)

	// 验证世界状态
	state, err := manager.GetWorldTimeState(world.ID)
	assert.NoError(t, err)
	assert.NotNil(t, state)
	assert.Equal(t, world.ID, state.WorldID)
	assert.Equal(t, world.GetCurrentDynamicTimeRate(), state.CurrentRate)
	assert.True(t, state.IsActive)
}

// TestDynamicTimeManager_RemoveWorld 测试移除世界
func TestDynamicTimeManager_RemoveWorld(t *testing.T) {
	db := setupTestDB(t)
	logger := logger.New("debug")
	manager := NewDynamicTimeManager(db, logger)

	// 创建测试世界
	world := createTestWorld(t, db, "test-world-001")

	// 添加世界
	err := manager.AddWorld(context.Background(), world.ID)
	require.NoError(t, err)

	// 移除世界
	err = manager.RemoveWorld(world.ID)
	assert.NoError(t, err)

	// 验证世界已被移除
	_, err = manager.GetWorldTimeState(world.ID)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "时间状态不存在")
}

// TestDynamicTimeManager_GetStats 测试获取统计信息
func TestDynamicTimeManager_GetStats(t *testing.T) {
	db := setupTestDB(t)
	logger := logger.New("debug")
	manager := NewDynamicTimeManager(db, logger)

	// 创建多个测试世界
	world1 := createTestWorld(t, db, "test-world-001")
	world2 := createTestWorld(t, db, "test-world-002")

	// 添加世界
	err := manager.AddWorld(context.Background(), world1.ID)
	require.NoError(t, err)
	err = manager.AddWorld(context.Background(), world2.ID)
	require.NoError(t, err)

	// 获取统计信息
	stats := manager.GetStats()

	assert.Equal(t, 2, stats["total_worlds"])
	assert.Equal(t, 2, stats["active_worlds"])
	assert.Equal(t, 0, stats["paused_worlds"])
	assert.Equal(t, 2, stats["total_players"])
	assert.Equal(t, false, stats["is_running"])
}

// TestDynamicTimeManager_StartStop 测试启动和停止
func TestDynamicTimeManager_StartStop(t *testing.T) {
	db := setupTestDB(t)
	logger := logger.New("debug")
	manager := NewDynamicTimeManager(db, logger)

	// 创建测试世界
	world := createTestWorld(t, db, "test-world-001")

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 启动管理器
	err := manager.Start(ctx)
	assert.NoError(t, err)
	assert.True(t, manager.isRunning)

	// 验证世界状态已初始化
	state, err := manager.GetWorldTimeState(world.ID)
	assert.NoError(t, err)
	assert.NotNil(t, state)

	// 停止管理器
	err = manager.Stop()
	assert.NoError(t, err)
	assert.False(t, manager.isRunning)
}

// TestWorldTimeState 测试世界时间状态
func TestWorldTimeState(t *testing.T) {
	state := &WorldTimeState{
		WorldID:         "test-world-001",
		CurrentRate:     2.0,
		LastRate:        1.0,
		LastUpdate:      time.Now(),
		LastTick:        time.Now(),
		GameTime:        100,
		TickCount:       10,
		IsActive:        true,
		IsPaused:        false,
		PlayerCount:     5,
		ScheduleEnabled: true,
		ActiveSchedule:  "白天高速",
		ConfigVersion:   1,
	}

	assert.Equal(t, "test-world-001", state.WorldID)
	assert.Equal(t, 2.0, state.CurrentRate)
	assert.Equal(t, int64(100), state.GameTime)
	assert.Equal(t, int64(10), state.TickCount)
	assert.True(t, state.IsActive)
	assert.False(t, state.IsPaused)
	assert.Equal(t, 5, state.PlayerCount)
	assert.True(t, state.ScheduleEnabled)
	assert.Equal(t, "白天高速", state.ActiveSchedule)
}

// TestTimeUpdateBatch 测试批量时间更新
func TestTimeUpdateBatch(t *testing.T) {
	batch := &TimeUpdateBatch{
		Updates: []WorldTimeUpdate{
			{
				WorldID:   "world-001",
				GameTime:  100,
				TickCount: 10,
				TimeRate:  2.0,
			},
			{
				WorldID:   "world-002",
				GameTime:  200,
				TickCount: 20,
				TimeRate:  3.0,
			},
		},
		Timestamp: time.Now(),
	}

	assert.Equal(t, 2, len(batch.Updates))
	assert.Equal(t, "world-001", batch.Updates[0].WorldID)
	assert.Equal(t, int64(100), batch.Updates[0].GameTime)
	assert.Equal(t, 2.0, batch.Updates[0].TimeRate)
}

// TestDynamicTimeManager_UpdateWorldTimeState 测试更新世界时间状态
func TestDynamicTimeManager_UpdateWorldTimeState(t *testing.T) {
	db := setupTestDB(t)
	logger := logger.New("debug")
	manager := NewDynamicTimeManager(db, logger)

	// 创建测试世界
	world := createTestWorld(t, db, "test-world-001")

	// 创建时间状态
	state := &WorldTimeState{
		WorldID:     world.ID,
		CurrentRate: 2.0,
		LastTick:    time.Now().Add(-30 * time.Second), // 30秒前
		GameTime:    0,
		TickCount:   0,
		IsActive:    true,
		IsPaused:    false,
	}

	now := time.Now()

	// 更新时间状态
	manager.updateWorldTimeState(state, world, now)

	// 验证更新结果
	assert.Greater(t, state.GameTime, int64(0), "游戏时间应该增加")
	assert.Equal(t, int64(1), state.TickCount, "心跳计数应该增加")
	assert.Equal(t, now, state.LastTick, "最后心跳时间应该更新")
	assert.Equal(t, now, state.LastUpdate, "最后更新时间应该更新")
}

// TestDynamicTimeManager_Integration 集成测试
func TestDynamicTimeManager_Integration(t *testing.T) {
	db := setupTestDB(t)
	logger := logger.New("debug")
	manager := NewDynamicTimeManager(db, logger)

	// 设置较短的检查间隔用于测试
	manager.checkInterval = 100 * time.Millisecond

	// 创建测试世界
	world := createTestWorld(t, db, "test-world-001")

	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()

	// 启动管理器
	err := manager.Start(ctx)
	require.NoError(t, err)

	// 等待一段时间让管理器运行
	time.Sleep(500 * time.Millisecond)

	// 获取世界状态
	state, err := manager.GetWorldTimeState(world.ID)
	require.NoError(t, err)

	// 验证状态
	assert.Equal(t, world.ID, state.WorldID)
	assert.Greater(t, state.CurrentRate, 0.0)
	assert.True(t, state.IsActive)

	// 停止管理器
	err = manager.Stop()
	assert.NoError(t, err)
}

// BenchmarkDynamicTimeManager_UpdateTimeRates 基准测试时间速率更新
func BenchmarkDynamicTimeManager_UpdateTimeRates(b *testing.B) {
	db := setupTestDB(&testing.T{})
	logger := logger.New("error") // 减少日志输出
	manager := NewDynamicTimeManager(db, logger)

	// 创建多个测试世界
	for i := 0; i < 100; i++ {
		worldID := fmt.Sprintf("test-world-%03d", i)
		createTestWorld(&testing.T{}, db, worldID)
		manager.AddWorld(context.Background(), worldID)
	}

	ctx := context.Background()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		manager.updateTimeRates(ctx)
	}
}

// BenchmarkDynamicTimeManager_BatchUpdateDatabase 基准测试批量数据库更新
func BenchmarkDynamicTimeManager_BatchUpdateDatabase(b *testing.B) {
	db := setupTestDB(&testing.T{})
	logger := logger.New("error") // 减少日志输出
	manager := NewDynamicTimeManager(db, logger)

	// 创建多个测试世界
	for i := 0; i < 100; i++ {
		worldID := fmt.Sprintf("test-world-%03d", i)
		createTestWorld(&testing.T{}, db, worldID)
		manager.AddWorld(context.Background(), worldID)
	}

	ctx := context.Background()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		manager.batchUpdateDatabase(ctx)
	}
}
