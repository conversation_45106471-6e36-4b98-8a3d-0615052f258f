2025/08/07 09:35:49 logger.go:83: [INFO] 正在启动AI文本游戏服务器 version=1.0.0
数据库配置: DBName=dev.db, Host=, Port=0
使用SQLite数据库: dev.db
2025/08/07 09:35:49 logger.go:83: [INFO] 数据库连接成功
2025/08/07 09:35:49 logger.go:83: [INFO] 开始执行数据库迁移
2025/08/07 09:35:49 logger.go:83: [INFO] 检查数据库兼容性
2025/08/07 09:35:49 logger.go:83: [INFO] 数据库信息 type=sqlite supports_jsonb=false supports_uuid=false
2025/08/07 09:35:49 logger.go:83: [INFO] 开发环境：使用GORM自动迁移

2025/08/07 09:35:49 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.027ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type='table' AND name="users"

2025/08/07 09:35:49 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.066ms] [34;1m[rows:8][0m SELECT sql FROM sqlite_master WHERE type IN ("table","index") AND tbl_name = "users" AND sql IS NOT NULL order by type = "table" desc

2025/08/07 09:35:49 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.026ms] [34;1m[rows:-][0m SELECT * FROM `users` LIMIT 1

2025/08/07 09:35:49 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.027ms] [34;1m[rows:1][0m PRAGMA foreign_keys

2025/08/07 09:35:49 [32m
[0m[33m[0.017ms] [34;1m[rows:-][0m SELECT sql FROM sqlite_master WHERE type = "table" AND tbl_name = "users" AND name = "users"

2025/08/07 09:35:49 [32m
[0m[33m[0.248ms] [34;1m[rows:0][0m CREATE TABLE `users__temp`  (`id` text,`external_id` text NOT NULL,`external_provider` text NOT NULL,`email` text NOT NULL,`display_name` text,`avatar_url` text,`game_roles` text DEFAULT "[""user""]",`status` text DEFAULT "active",`preferences` text DEFAULT "{}",`id_p_claims` text DEFAULT "{}",`created_at` datetime,`updated_at` datetime,`last_login_at` datetime,`deleted_at` datetime,`profile` text DEFAULT "{}",`last_active_at` datetime,PRIMARY KEY (`id`))

2025/08/07 09:35:49 [32m
[0m[33m[0.023ms] [34;1m[rows:0][0m INSERT INTO `users__temp`(`id`,`external_id`,`external_provider`,`email`,`display_name`,`avatar_url`,`game_roles`,`status`,`preferences`,`id_p_claims`,`created_at`,`updated_at`,`last_login_at`,`deleted_at`,`profile`,`last_active_at`) SELECT `id`,`external_id`,`external_provider`,`email`,`display_name`,`avatar_url`,`game_roles`,`status`,`preferences`,`id_p_claims`,`created_at`,`updated_at`,`last_login_at`,`deleted_at`,`profile`,`last_active_at` FROM `users`

2025/08/07 09:35:49 [32m
[0m[33m[0.549ms] [34;1m[rows:0][0m DROP TABLE `users`

2025/08/07 09:35:49 [31;1m [35;1merror in trigger update_user_stats_updated_at: no such column: id
[0m[33m[0.165ms] [34;1m[rows:0][0m ALTER TABLE `users__temp` RENAME TO `users`
2025/08/07 09:35:49 logger.go:103: [FATAL] 数据库迁移失败 error=开发环境数据库迁移失败: 自动迁移失败: error in trigger update_user_stats_updated_at: no such column: id
