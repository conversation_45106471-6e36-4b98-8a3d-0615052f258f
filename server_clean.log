2025/08/07 09:41:05 logger.go:83: [INFO] 正在启动AI文本游戏服务器 version=1.0.0
数据库配置: DBName=dev.db, Host=, Port=0
使用SQLite数据库: dev.db
2025/08/07 09:41:05 logger.go:83: [INFO] 数据库连接成功
2025/08/07 09:41:05 logger.go:83: [INFO] 开始执行数据库迁移
2025/08/07 09:41:05 logger.go:83: [INFO] 检查数据库兼容性
2025/08/07 09:41:05 logger.go:83: [INFO] 数据库信息 type=sqlite supports_jsonb=false supports_uuid=false
2025/08/07 09:41:05 logger.go:83: [INFO] 开发环境：使用GORM自动迁移

2025/08/07 09:41:05 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.028ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type='table' AND name="users"

2025/08/07 09:41:05 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.561ms] [34;1m[rows:0][0m CREATE TABLE `users` (`id` text,`external_id` text NOT NULL,`external_provider` text NOT NULL,`email` text NOT NULL,`display_name` text,`avatar_url` text,`profile` text DEFAULT "{}",`last_active_at` datetime,`game_roles` text DEFAULT "[""user""]",`status` text DEFAULT "active",`preferences` text DEFAULT "{}",`id_p_claims` text DEFAULT "{}",`created_at` datetime,`updated_at` datetime,`last_login_at` datetime,`deleted_at` datetime,PRIMARY KEY (`id`))

2025/08/07 09:41:05 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.860ms] [34;1m[rows:0][0m CREATE INDEX `idx_users_deleted_at` ON `users`(`deleted_at`)

2025/08/07 09:41:05 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[7.060ms] [34;1m[rows:0][0m CREATE INDEX `idx_users_created_at` ON `users`(`created_at`)

2025/08/07 09:41:05 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.876ms] [34;1m[rows:0][0m CREATE INDEX `idx_users_status` ON `users`(`status`)

2025/08/07 09:41:05 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[8.025ms] [34;1m[rows:0][0m CREATE INDEX `idx_users_last_active_at` ON `users`(`last_active_at`)

2025/08/07 09:41:05 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[7.848ms] [34;1m[rows:0][0m CREATE INDEX `idx_users_email` ON `users`(`email`)

2025/08/07 09:41:05 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[7.028ms] [34;1m[rows:0][0m CREATE INDEX `idx_users_external_provider` ON `users`(`external_provider`)

2025/08/07 09:41:05 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.888ms] [34;1m[rows:0][0m CREATE INDEX `idx_users_external_id` ON `users`(`external_id`)

2025/08/07 09:41:05 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.034ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type='table' AND name="user_stats"

2025/08/07 09:41:05 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[7.742ms] [34;1m[rows:0][0m CREATE TABLE `user_stats` (`user_id` text,`total_play_time` integer DEFAULT 0,`worlds_created` integer DEFAULT 0,`worlds_joined` integer DEFAULT 0,`achievements` text DEFAULT "[]",`level` integer DEFAULT 1,`experience` integer DEFAULT 0,`updated_at` datetime,PRIMARY KEY (`user_id`),CONSTRAINT `fk_users_stats` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`))

2025/08/07 09:41:05 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[7.069ms] [34;1m[rows:0][0m CREATE INDEX `idx_user_stats_experience` ON `user_stats`(`experience`)

2025/08/07 09:41:05 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.823ms] [34;1m[rows:0][0m CREATE INDEX `idx_user_stats_level` ON `user_stats`(`level`)

2025/08/07 09:41:05 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.025ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type='table' AND name="worlds"

2025/08/07 09:41:05 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.765ms] [34;1m[rows:0][0m CREATE TABLE `worlds` (`id` text,`name` text NOT NULL,`description` text,`creator_id` text NOT NULL,`world_config` text NOT NULL,`world_state` text NOT NULL,`access_settings` text DEFAULT "{}",`tags` text DEFAULT "[]",`time_config` text DEFAULT "{}",`status` text DEFAULT "active",`is_public` numeric DEFAULT false,`max_players` integer DEFAULT 10,`current_players` integer DEFAULT 0,`game_time` integer DEFAULT 0,`created_at` datetime,`updated_at` datetime,`deleted_at` datetime,PRIMARY KEY (`id`),CONSTRAINT `fk_users_worlds` FOREIGN KEY (`creator_id`) REFERENCES `users`(`id`))

2025/08/07 09:41:05 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[8.039ms] [34;1m[rows:0][0m CREATE INDEX `idx_worlds_deleted_at` ON `worlds`(`deleted_at`)

2025/08/07 09:41:05 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.871ms] [34;1m[rows:0][0m CREATE INDEX `idx_worlds_created_at` ON `worlds`(`created_at`)

2025/08/07 09:41:05 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.888ms] [34;1m[rows:0][0m CREATE INDEX `idx_worlds_is_public` ON `worlds`(`is_public`)

2025/08/07 09:41:05 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[7.035ms] [34;1m[rows:0][0m CREATE INDEX `idx_worlds_status` ON `worlds`(`status`)

2025/08/07 09:41:05 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.892ms] [34;1m[rows:0][0m CREATE INDEX `idx_worlds_creator_id` ON `worlds`(`creator_id`)

2025/08/07 09:41:05 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.022ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type='table' AND name="scenes"

2025/08/07 09:41:05 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[7.685ms] [34;1m[rows:0][0m CREATE TABLE `scenes` (`id` text,`world_id` text NOT NULL,`name` text NOT NULL,`description` text,`scene_type` text DEFAULT "normal",`properties` text DEFAULT "{}",`entities_present` text DEFAULT "[]",`connected_scenes` text DEFAULT "{}",`environment` text DEFAULT "{}",`tags` text DEFAULT "[]",`access_rules` text DEFAULT "{}",`connections` text DEFAULT "[]",`status` text DEFAULT "active",`created_at` datetime,`updated_at` datetime,`deleted_at` datetime,PRIMARY KEY (`id`),CONSTRAINT `fk_worlds_scenes` FOREIGN KEY (`world_id`) REFERENCES `worlds`(`id`))

2025/08/07 09:41:05 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.898ms] [34;1m[rows:0][0m CREATE INDEX `idx_scenes_deleted_at` ON `scenes`(`deleted_at`)

2025/08/07 09:41:05 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[7.075ms] [34;1m[rows:0][0m CREATE INDEX `idx_scenes_status` ON `scenes`(`status`)

2025/08/07 09:41:05 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.880ms] [34;1m[rows:0][0m CREATE INDEX `idx_scenes_scene_type` ON `scenes`(`scene_type`)

2025/08/07 09:41:05 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.911ms] [34;1m[rows:0][0m CREATE INDEX `idx_scenes_world_id` ON `scenes`(`world_id`)

2025/08/07 09:41:05 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.024ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type='table' AND name="characters"

2025/08/07 09:41:05 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.871ms] [34;1m[rows:0][0m CREATE TABLE `characters` (`id` text,`world_id` text NOT NULL,`user_id` text,`name` text NOT NULL,`description` text,`character_type` text DEFAULT "player",`current_scene_id` text,`traits` text DEFAULT "[]",`memories` text DEFAULT "{}",`experiences` text DEFAULT "{}",`relationships` text DEFAULT "{}",`characteristics` text DEFAULT "{}",`is_primary` numeric DEFAULT false,`display_order` integer DEFAULT 0,`last_active_at` datetime,`status` text DEFAULT "active",`last_action_at` datetime,`created_at` datetime,`updated_at` datetime,`deleted_at` datetime,PRIMARY KEY (`id`),CONSTRAINT `fk_worlds_characters` FOREIGN KEY (`world_id`) REFERENCES `worlds`(`id`),CONSTRAINT `fk_scenes_characters` FOREIGN KEY (`current_scene_id`) REFERENCES `scenes`(`id`),CONSTRAINT `fk_users_characters` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`))

2025/08/07 09:41:05 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.896ms] [34;1m[rows:0][0m CREATE INDEX `idx_characters_deleted_at` ON `characters`(`deleted_at`)

2025/08/07 09:41:05 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.907ms] [34;1m[rows:0][0m CREATE INDEX `idx_characters_status` ON `characters`(`status`)

2025/08/07 09:41:05 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.883ms] [34;1m[rows:0][0m CREATE INDEX `idx_characters_last_active_at` ON `characters`(`last_active_at`)

2025/08/07 09:41:05 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[7.068ms] [34;1m[rows:0][0m CREATE INDEX `idx_characters_is_primary` ON `characters`(`is_primary`)

2025/08/07 09:41:05 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.875ms] [34;1m[rows:0][0m CREATE INDEX `idx_characters_current_scene_id` ON `characters`(`current_scene_id`)

2025/08/07 09:41:05 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[7.030ms] [34;1m[rows:0][0m CREATE INDEX `idx_characters_character_type` ON `characters`(`character_type`)

2025/08/07 09:41:05 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[7.848ms] [34;1m[rows:0][0m CREATE INDEX `idx_characters_user_id` ON `characters`(`user_id`)

2025/08/07 09:41:05 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.890ms] [34;1m[rows:0][0m CREATE INDEX `idx_characters_world_id` ON `characters`(`world_id`)

2025/08/07 09:41:05 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.025ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type='table' AND name="entities"

2025/08/07 09:41:05 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[7.926ms] [34;1m[rows:0][0m CREATE TABLE `entities` (`id` text,`world_id` text NOT NULL,`name` text NOT NULL,`description` text,`entity_type` text NOT NULL,`properties` text DEFAULT "{}",`traits` text DEFAULT "[]",`container_entity_id` text,`version` integer NOT NULL DEFAULT 1,`tags` text DEFAULT "[]",`current_scene_id` text,`owner_id` text,`status` text DEFAULT "active",`created_at` datetime,`updated_at` datetime,`deleted_at` datetime,PRIMARY KEY (`id`),CONSTRAINT `fk_worlds_entities` FOREIGN KEY (`world_id`) REFERENCES `worlds`(`id`),CONSTRAINT `fk_entities_contained_entities` FOREIGN KEY (`container_entity_id`) REFERENCES `entities`(`id`),CONSTRAINT `fk_characters_owned_entities` FOREIGN KEY (`owner_id`) REFERENCES `characters`(`id`),CONSTRAINT `fk_scenes_entities` FOREIGN KEY (`current_scene_id`) REFERENCES `scenes`(`id`))

2025/08/07 09:41:05 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[7.827ms] [34;1m[rows:0][0m CREATE INDEX `idx_entities_deleted_at` ON `entities`(`deleted_at`)

2025/08/07 09:41:05 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[7.033ms] [34;1m[rows:0][0m CREATE INDEX `idx_entities_status` ON `entities`(`status`)

2025/08/07 09:41:05 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.888ms] [34;1m[rows:0][0m CREATE INDEX `idx_entities_owner_id` ON `entities`(`owner_id`)

2025/08/07 09:41:05 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.986ms] [34;1m[rows:0][0m CREATE INDEX `idx_entities_current_scene_id` ON `entities`(`current_scene_id`)

2025/08/07 09:41:06 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[5.854ms] [34;1m[rows:0][0m CREATE INDEX `idx_entities_container_entity_id` ON `entities`(`container_entity_id`)

2025/08/07 09:41:06 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.862ms] [34;1m[rows:0][0m CREATE INDEX `idx_entities_entity_type` ON `entities`(`entity_type`)

2025/08/07 09:41:06 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[7.070ms] [34;1m[rows:0][0m CREATE INDEX `idx_entities_world_id` ON `entities`(`world_id`)

2025/08/07 09:41:06 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.027ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type='table' AND name="events"

2025/08/07 09:41:06 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[7.720ms] [34;1m[rows:0][0m CREATE TABLE `events` (`id` text,`world_id` text NOT NULL,`scene_id` text,`creator_id` text,`event_type` text NOT NULL,`name` text,`description` text,`event_data` text DEFAULT "{}",`participants` text DEFAULT "[]",`status` text DEFAULT "pending",`priority` integer DEFAULT 0,`scheduled_at` datetime,`processed_at` datetime,`result` text,`created_at` datetime,`updated_at` datetime,`deleted_at` datetime,PRIMARY KEY (`id`),CONSTRAINT `fk_characters_created_events` FOREIGN KEY (`creator_id`) REFERENCES `characters`(`id`),CONSTRAINT `fk_scenes_events` FOREIGN KEY (`scene_id`) REFERENCES `scenes`(`id`),CONSTRAINT `fk_worlds_events` FOREIGN KEY (`world_id`) REFERENCES `worlds`(`id`))

2025/08/07 09:41:06 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[8.005ms] [34;1m[rows:0][0m CREATE INDEX `idx_events_deleted_at` ON `events`(`deleted_at`)

2025/08/07 09:41:06 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[7.878ms] [34;1m[rows:0][0m CREATE INDEX `idx_events_scheduled_at` ON `events`(`scheduled_at`)

2025/08/07 09:41:06 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[7.990ms] [34;1m[rows:0][0m CREATE INDEX `idx_events_priority` ON `events`(`priority`)

2025/08/07 09:41:06 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[7.874ms] [34;1m[rows:0][0m CREATE INDEX `idx_events_status` ON `events`(`status`)

2025/08/07 09:41:06 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[7.971ms] [34;1m[rows:0][0m CREATE INDEX `idx_events_event_type` ON `events`(`event_type`)

2025/08/07 09:41:06 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[7.888ms] [34;1m[rows:0][0m CREATE INDEX `idx_events_creator_id` ON `events`(`creator_id`)

2025/08/07 09:41:06 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.851ms] [34;1m[rows:0][0m CREATE INDEX `idx_events_scene_id` ON `events`(`scene_id`)

2025/08/07 09:41:06 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[8.065ms] [34;1m[rows:0][0m CREATE INDEX `idx_events_world_id` ON `events`(`world_id`)

2025/08/07 09:41:06 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[0.030ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type='table' AND name="ai_interactions"

2025/08/07 09:41:06 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.729ms] [34;1m[rows:0][0m CREATE TABLE `ai_interactions` (`id` text,`world_id` text,`user_id` text,`interaction_type` text NOT NULL,`prompt` text NOT NULL,`response` text,`response_schema` text,`token_usage` integer,`response_time` integer,`status` text DEFAULT "pending",`error_message` text,`created_at` datetime,PRIMARY KEY (`id`),CONSTRAINT `fk_ai_interactions_world` FOREIGN KEY (`world_id`) REFERENCES `worlds`(`id`),CONSTRAINT `fk_ai_interactions_user` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`))

2025/08/07 09:41:06 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[6.916ms] [34;1m[rows:0][0m CREATE INDEX `idx_ai_interactions_created_at` ON `ai_interactions`(`created_at`)

2025/08/07 09:41:06 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[8.026ms] [34;1m[rows:0][0m CREATE INDEX `idx_ai_interactions_status` ON `ai_interactions`(`status`)

2025/08/07 09:41:06 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[7.867ms] [34;1m[rows:0][0m CREATE INDEX `idx_ai_interactions_interaction_type` ON `ai_interactions`(`interaction_type`)

2025/08/07 09:41:06 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[7.004ms] [34;1m[rows:0][0m CREATE INDEX `idx_ai_interactions_user_id` ON `ai_interactions`(`user_id`)

2025/08/07 09:41:06 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:35
[0m[33m[7.773ms] [34;1m[rows:0][0m CREATE INDEX `idx_ai_interactions_world_id` ON `ai_interactions`(`world_id`)

2025/08/07 09:41:06 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:174
[0m[33m[0.026ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type='table' AND name="users"

2025/08/07 09:41:06 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:149
[0m[33m[6.985ms] [34;1m[rows:0][0m 
CREATE TRIGGER update_users_updated_at 
    AFTER UPDATE ON users 
    FOR EACH ROW 
    BEGIN 
        UPDATE users SET updated_at = datetime('now') WHERE id = NEW.id;
    END;

2025/08/07 09:41:06 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:174
[0m[33m[0.037ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type='table' AND name="user_stats"

2025/08/07 09:41:06 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:149
[0m[33m[6.779ms] [34;1m[rows:0][0m 
CREATE TRIGGER update_user_stats_updated_at 
    AFTER UPDATE ON user_stats 
    FOR EACH ROW 
    BEGIN 
        UPDATE user_stats SET updated_at = datetime('now') WHERE id = NEW.id;
    END;

2025/08/07 09:41:06 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:174
[0m[33m[0.021ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type='table' AND name="worlds"

2025/08/07 09:41:06 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:149
[0m[33m[6.815ms] [34;1m[rows:0][0m 
CREATE TRIGGER update_worlds_updated_at 
    AFTER UPDATE ON worlds 
    FOR EACH ROW 
    BEGIN 
        UPDATE worlds SET updated_at = datetime('now') WHERE id = NEW.id;
    END;

2025/08/07 09:41:06 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:174
[0m[33m[0.019ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type='table' AND name="characters"

2025/08/07 09:41:06 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:149
[0m[33m[7.016ms] [34;1m[rows:0][0m 
CREATE TRIGGER update_characters_updated_at 
    AFTER UPDATE ON characters 
    FOR EACH ROW 
    BEGIN 
        UPDATE characters SET updated_at = datetime('now') WHERE id = NEW.id;
    END;

2025/08/07 09:41:06 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:174
[0m[33m[0.020ms] [34;1m[rows:-][0m SELECT count(*) FROM sqlite_master WHERE type='table' AND name="scenes"

2025/08/07 09:41:06 [32m/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/internal/migration/smart_migrator.go:149
[0m[33m[6.855ms] [34;1m[rows:0][0m 
CREATE TRIGGER update_scenes_updated_at 
    AFTER UPDATE ON scenes 
    FOR EACH ROW 
    BEGIN 
        UPDATE scenes SET updated_at = datetime('now') WHERE id = NEW.id;
    END;
2025/08/07 09:41:06 logger.go:83: [INFO] 开发环境数据库迁移成功
2025/08/07 09:41:06 logger.go:83: [INFO] 数据库迁移完成
[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

2025/08/07 09:41:06 logger.go:83: [INFO] 异步任务管理器已启动 worker_count=5
2025/08/07 09:41:06 logger.go:83: [INFO] 数据流处理器已启动
[GIN-debug] GET    /health                   --> ai-text-game-iam-npc/internal/routes.SetupRoutes.func1 (6 handlers)
[GIN-debug] GET    /api/health               --> ai-text-game-iam-npc/internal/routes.SetupRoutes.func1 (6 handlers)
[GIN-debug] GET    /api/v1/auth/:provider/url --> ai-text-game-iam-npc/internal/handlers.(*AuthHandler).GetAuthURL-fm (6 handlers)
[GIN-debug] GET    /api/v1/auth/:provider/callback --> ai-text-game-iam-npc/internal/handlers.(*AuthHandler).HandleCallback-fm (6 handlers)
[GIN-debug] POST   /api/v1/auth/refresh      --> ai-text-game-iam-npc/internal/handlers.(*AuthHandler).RefreshToken-fm (6 handlers)
[GIN-debug] GET    /api/v1/validation/config --> ai-text-game-iam-npc/internal/handlers.(*ValidationHandler).GetValidationConfig-fm (6 handlers)
[GIN-debug] GET    /api/v1/user/profile      --> ai-text-game-iam-npc/internal/handlers.(*AuthHandler).GetProfile-fm (8 handlers)
[GIN-debug] PUT    /api/v1/user/profile      --> ai-text-game-iam-npc/internal/handlers.(*AuthHandler).UpdateProfile-fm (8 handlers)
[GIN-debug] POST   /api/v1/validation/validate --> ai-text-game-iam-npc/internal/handlers.(*ValidationHandler).ValidateContent-fm (8 handlers)
[GIN-debug] POST   /api/v1/validation/batch-validate --> ai-text-game-iam-npc/internal/handlers.(*ValidationHandler).BatchValidateContent-fm (8 handlers)
[GIN-debug] GET    /api/v1/validation/stats  --> ai-text-game-iam-npc/internal/handlers.(*ValidationHandler).GetValidationStats-fm (8 handlers)
[GIN-debug] POST   /api/v1/ai/generate       --> ai-text-game-iam-npc/internal/handlers.(*AIHandler).GenerateContent-fm (8 handlers)
[GIN-debug] POST   /api/v1/ai/generate/scene --> ai-text-game-iam-npc/internal/handlers.(*AIHandler).GenerateScene-fm (8 handlers)
[GIN-debug] POST   /api/v1/ai/generate/character --> ai-text-game-iam-npc/internal/handlers.(*AIHandler).GenerateCharacter-fm (8 handlers)
[GIN-debug] POST   /api/v1/ai/generate/event --> ai-text-game-iam-npc/internal/handlers.(*AIHandler).GenerateEvent-fm (8 handlers)
[GIN-debug] GET    /api/v1/ai/history        --> ai-text-game-iam-npc/internal/handlers.(*AIHandler).GetInteractionHistory-fm (8 handlers)
[GIN-debug] GET    /api/v1/ai/stats          --> ai-text-game-iam-npc/internal/handlers.(*AIHandler).GetTokenUsageStats-fm (8 handlers)
[GIN-debug] POST   /api/v1/game/worlds       --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).CreateWorld-fm (8 handlers)
[GIN-debug] GET    /api/v1/game/worlds/:world_id --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).GetWorld-fm (8 handlers)
[GIN-debug] PUT    /api/v1/game/worlds/:world_id --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).UpdateWorld-fm (8 handlers)
[GIN-debug] DELETE /api/v1/game/worlds/:world_id --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).DeleteWorld-fm (8 handlers)
[GIN-debug] POST   /api/v1/game/worlds/:world_id/join --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).JoinWorld-fm (8 handlers)
[GIN-debug] POST   /api/v1/game/worlds/:world_id/leave --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).LeaveWorld-fm (8 handlers)
[GIN-debug] GET    /api/v1/game/worlds/:world_id/characters --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).GetWorldCharacters-fm (8 handlers)
[GIN-debug] GET    /api/v1/game/worlds/:world_id/state --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).GetWorldState-fm (8 handlers)
[GIN-debug] POST   /api/v1/game/worlds/:world_id/time --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).UpdateWorldTime-fm (8 handlers)
[GIN-debug] POST   /api/v1/game/worlds/:world_id/tick --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).ProcessWorldTick-fm (8 handlers)
[GIN-debug] GET    /api/v1/game/my-worlds    --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).GetMyWorlds-fm (8 handlers)
[GIN-debug] GET    /api/v1/game/public-worlds --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).GetPublicWorlds-fm (8 handlers)
[GIN-debug] POST   /api/v1/game/characters   --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).CreateCharacter-fm (8 handlers)
[GIN-debug] GET    /api/v1/game/characters/:character_id --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).GetCharacter-fm (8 handlers)
[GIN-debug] PUT    /api/v1/game/characters/:character_id --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).UpdateCharacter-fm (8 handlers)
[GIN-debug] DELETE /api/v1/game/characters/:character_id --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).DeleteCharacter-fm (8 handlers)
[GIN-debug] POST   /api/v1/game/characters/:character_id/move --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).MoveCharacter-fm (8 handlers)
[GIN-debug] POST   /api/v1/game/characters/:character_id/traits --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).AddCharacterTrait-fm (8 handlers)
[GIN-debug] POST   /api/v1/game/characters/:character_id/memories --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).AddCharacterMemory-fm (8 handlers)
[GIN-debug] POST   /api/v1/game/characters/:character_id/experiences --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).AddCharacterExperience-fm (8 handlers)
[GIN-debug] POST   /api/v1/game/characters/:character_id/actions --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).PerformAction-fm (8 handlers)
[GIN-debug] POST   /api/v1/game/characters/:character_id/interact/:target_character_id --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).InteractWithCharacter-fm (8 handlers)
[GIN-debug] POST   /api/v1/game/characters/:character_id/speak --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).SpeakInScene-fm (8 handlers)
[GIN-debug] GET    /api/v1/game/my-characters --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).GetMyCharacters-fm (8 handlers)
[GIN-debug] POST   /api/v1/game/scenes       --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).CreateScene-fm (8 handlers)
[GIN-debug] GET    /api/v1/game/scenes/:scene_id --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).GetScene-fm (8 handlers)
[GIN-debug] POST   /api/v1/game/events       --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).CreateEvent-fm (8 handlers)
[GIN-debug] POST   /api/v1/game/events/:event_id/process --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).ProcessEvent-fm (8 handlers)
[GIN-debug] POST   /api/v1/game/events/trigger --> ai-text-game-iam-npc/internal/handlers.(*GameHandler).TriggerEvent-fm (8 handlers)
[GIN-debug] GET    /api/v1/admin/stats       --> ai-text-game-iam-npc/internal/routes.SetupRoutes.func2 (7 handlers)
[GIN-debug] GET    /api/v1/admin/users       --> ai-text-game-iam-npc/internal/routes.SetupRoutes.func3 (7 handlers)
[GIN-debug] GET    /api/v1/admin/worlds      --> ai-text-game-iam-npc/internal/routes.SetupRoutes.func4 (7 handlers)
[GIN-debug] GET    /static/*filepath         --> github.com/gin-gonic/gin.(*RouterGroup).createStaticHandler.func1 (6 handlers)
[GIN-debug] HEAD   /static/*filepath         --> github.com/gin-gonic/gin.(*RouterGroup).createStaticHandler.func1 (6 handlers)
[GIN-debug] GET    /favicon.ico              --> github.com/gin-gonic/gin.(*RouterGroup).StaticFile.func1 (6 handlers)
[GIN-debug] HEAD   /favicon.ico              --> github.com/gin-gonic/gin.(*RouterGroup).StaticFile.func1 (6 handlers)
[GIN-debug] GET    /assets/*filepath         --> github.com/gin-gonic/gin.(*RouterGroup).createStaticHandler.func1 (6 handlers)
[GIN-debug] HEAD   /assets/*filepath         --> github.com/gin-gonic/gin.(*RouterGroup).createStaticHandler.func1 (6 handlers)
[GIN-debug] GET    /vite.svg                 --> github.com/gin-gonic/gin.(*RouterGroup).StaticFile.func1 (6 handlers)
[GIN-debug] HEAD   /vite.svg                 --> github.com/gin-gonic/gin.(*RouterGroup).StaticFile.func1 (6 handlers)
2025/08/07 09:41:06 logger.go:83: [INFO] 服务器启动 port=8080 environment=development
